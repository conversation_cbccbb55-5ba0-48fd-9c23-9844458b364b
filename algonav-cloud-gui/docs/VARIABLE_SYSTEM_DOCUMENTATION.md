# Variable System Documentation

## Übersicht

Das Variable System in AlgoNav Cloud GUI ermöglicht die hierarchische Verwaltung von Konfigurationsvariablen mit Override-Mechanismen. Variablen können auf verschiedenen Ebenen definiert werden und folgen einer klaren Vererbungshierarchie.

## Datenbank-Schema

### Tabellen-Struktur

```sql
-- Template-Variablen (Basis-Ebene)
templates_variables (
  id SERIAL PRIMARY KEY,
  template_id INTEGER REFERENCES templates(id),
  name TEXT NOT NULL,
  data JSONB,
  gui JSONB,  -- GUI-Konfiguration für Input-Komponenten
  links JSONB DEFAULT '[]'::jsonb
)

-- Kategorie-Variablen (Override-Ebene 1)
categories_variables (
  id SERIAL PRIMARY KEY,
  category_id INTEGER REFERENCES categories(id),
  name TEXT NOT NULL,
  data JSONB,
  gui JSONB,
  links JSONB DEFAULT '[]'::jsonb
)

-- Dataset-Variablen (Override-Ebene 2)
datasets_variables (
  id SERIAL PRIMARY KEY,
  dataset_id INTEGER REFERENCES datasets(id),
  name TEXT NOT NULL,
  data JSONB,
  gui JSONB,
  links JSONB DEFAULT '[]'::jsonb
)
```

### GUI-Konfiguration Format

```json
{
  "component_id": "Checkbox|CheckboxGroup|TextInput|NumberInput|...",
  "label": "Human-readable label",
  "tooltip": "Help text",
  "group": "Section grouping",
  "order": 100,
  "items": [  // Für CheckboxGroup
    {"label": "GPS", "value": "G"},
    {"label": "Galileo", "value": "E"}
  ],
  "min_checked": 1,  // Validierung
  "max_checked": 999,
  "msg_below_min": "Error message"
}
```

## Hierarchie und Override-System

### Vererbungshierarchie (niedrigste zu höchste Priorität)

1. **Template-Ebene** (Basis)
   - Definiert Standard-Werte für alle Jobs mit diesem Template
   - Niedrigste Priorität

2. **Kategorie-Ebene** (Override 1)
   - Überschreibt Template-Werte für spezifische Kategorien
   - Mittlere Priorität
   - Vererbung: Unterkategorien erben von Elternkategorien

3. **Dataset-Ebene** (Override 2)
   - Überschreibt alle höheren Ebenen für spezifische Datasets
   - Höchste Priorität

### Override-Regeln

- **Aktive Variable**: Die Variable mit der höchsten Priorität in der Hierarchie
- **Überschriebene Variable**: Variable existiert, wird aber von höherer Ebene überschrieben
- **Vererbung**: Unterkategorien erben Variablen von Elternkategorien
- **Vollständige Überschreibung**: Höhere Ebene überschreibt komplett (nicht nur einzelne Eigenschaften)

## Datenbank-Funktionen

### Haupt-RPC-Funktion

```sql
get_variable_tree_with_context(p_template_id INTEGER, p_user_id UUID)
```

**Rückgabe-Struktur:**
```json
{
  "template_id": 8,
  "template_variables": [
    {
      "name": "DISABLE_PHASE",
      "data": false,
      "gui": {...},
      "source_level": "Template",
      "is_active": true,
      "is_overridden": false
    }
  ],
  "tree": [
    {
      "id": 19,
      "name": "Root-Kampagne",
      "type": "category",
      "level": 0,
      "variables": [...],
      "children": [
        {
          "id": 21,
          "name": "Sub-Kampagne1",
          "type": "category",
          "level": 1,
          "datasets": [
            {
              "id": 41,
              "name": "mergetest",
              "type": "dataset",
              "variables": [
                {
                  "name": "GNSSES",
                  "data": ["G", "E", "R"],
                  "gui": {...}
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## API-Endpunkt

### Route: `/api/variable-tree`

**Datei:** `algonav-cloud-gui/app/api/variable-tree/route.ts`

**Parameter:**
- `templateId`: Template ID (required)

**Authentifizierung:** `withAuth` Middleware

**Rückgabe:** `VariableTreeResponse`

### TypeScript Interfaces

```typescript
interface VariableWithContext {
  name: string;
  source_level: 'Template' | 'Category' | 'Dataset';
  is_active: boolean;
  is_overridden: boolean;
  value: any;
  data?: any; // Alias für value
  gui?: any;  // GUI-Konfiguration
  links?: any[];
}

interface VariableTreeNode {
  id: number;
  name: string;
  type: 'category' | 'dataset';
  level?: number;
  variables: VariableWithContext[];
  datasets?: VariableTreeNode[];
  children?: VariableTreeNode[];
}

interface VariableTreeResponse {
  template_id: number;
  template_variables: VariableWithContext[];
  tree: VariableTreeNode[];
}
```

## React Hooks

### useVariableTree

**Datei:** `algonav-cloud-gui/lib/hooks/useVariableTree.ts`

**Funktionalität:**
```typescript
const {
  data,                    // VariableTreeResponse
  loading,                 // boolean
  error,                   // string | null
  refetch,                 // () => Promise<void>
  getVariablesByName,      // (name: string) => VariableWithContext[]
  getVariableState,        // (name: string) => VariableState
  getVariableStateForContext // (name: string, nodeId?, datasetId?) => VariableState
} = useVariableTree({ templateId, enabled: true });
```

### useVariableTreeState

**Datei:** `algonav-cloud-gui/lib/hooks/useVariableTreeState.ts`

**Funktionalität:**
```typescript
const {
  variableChanges,         // Map<string, any>
  hasChanges,              // boolean
  updateVariable,          // (name, value, original, nodeId?, nodeType?) => void
  resetVariable,           // (name: string) => void
  resetAllChanges,         // () => void
  saveChanges,             // () => Promise<void>
  isSaving,                // boolean
  saveError,               // string | null
  getVariableValue,        // (name: string, originalValue: any) => any
  isVariableChanged        // (name: string) => boolean
} = useVariableTreeState({ templateId, onSaveSuccess?, onSaveError? });
```

**React Query Integration:**
- Verwendet `useMutation` für Template-, Kategorie- und Dataset-Updates
- Automatische Cache-Invalidierung nach erfolgreichen Änderungen
- Optimistische Updates und Error-Handling
- Folgt dem etablierten API-Pattern der Codebase

### Wichtige Funktionen

1. **getVariablesByName(variableName)**
   - Findet alle Instanzen einer Variable in der gesamten Hierarchie
   - Sucht in: Template-Variablen, Kategorie-Variablen, Dataset-Variablen
   - Normalisiert Dataset/Kategorie-Variablen zu VariableWithContext Format

2. **getVariableState(variableName)**
   - Berechnet den Status einer Variable basierend auf Override-Regeln
   - Rückgabe: `{ primaryState, secondaryState, counts, activeVariable, overriddenBy }`

3. **getVariableStateForContext(variableName, nodeId?, datasetId?)**
   - Wie getVariableState, aber für spezifischen Kontext (Kategorie/Dataset)

## UI-Komponenten

### VariableTreeView

**Datei:** `algonav-cloud-gui/components/template/VariableTreeView.tsx`

**Funktionalität:**
- Zeigt hierarchischen Baum von Kategorien und Datasets
- Toggle zwischen Status-Badges und Input-Komponenten
- Zwei-Spalten-Layout: Hierarchie links, Variablen rechts

### VariableInputRenderer

**Datei:** `algonav-cloud-gui/components/template/VariableInputRenderer.tsx`

**Funktionalität:**
- Rendert Variable als Input-Komponente basierend auf GUI-Konfiguration
- Konvertiert VariableWithContext zu TemplateVariable Format
- Zeigt Status-Indikatoren und Override-Markierungen

### InputRenderer

**Datei:** `algonav-cloud-gui/components/template/InputRenderer.tsx`

**Funktionalität:**
- Rendert spezifische Input-Komponenten basierend auf `component_id`
- Unterstützt: Checkbox, CheckboxGroup, TextInput, NumberInput, etc.
- Verwendet Material-UI Komponenten

## Datenfluss

### 1. Datenbank → API

```
Datenbank (RPC) → get_variable_tree_with_context()
                ↓
API Route (/api/variable-tree) → VariableTreeResponse
```

### 2. API → React Hooks

```
API Response → useVariableTree Hook (Data Fetching)
             ↓
Normalisierte Daten + Helper-Funktionen

API Functions → useVariableTreeState Hook (State Management)
              ↓
React Query Mutations + Change Tracking
```

### 3. Hooks → UI Komponenten

```
useVariableTree → VariableTreeView (Display)
                ↓
getVariablesByName() → VariableInputRenderer
                     ↓
GUI Config → InputRenderer → Material-UI Komponente

useVariableTreeState → VariableInputRenderer (State Management)
                     ↓
updateVariable() → React Query Mutations → API Functions
```

### 4. Variable Lookup Prozess

```
1. getAllVariablesForContext(nodeId, datasetId)
   ↓
2. getVariablesByName(variableName) für jede Variable
   ↓
3. Findet alle Instanzen in Hierarchie
   ↓
4. getVariableStateForContext() berechnet aktive Variable
   ↓
5. VariableInputRenderer rendert Input-Komponente
```

## Debugging

### Console Logs

- `useVariableTree`: API-Aufrufe und Daten-Normalisierung
- `VariableTreeView`: Tree-Building und Variable-Lookup
- `VariableInputRenderer`: Variable-Rendering und GUI-Konfiguration

### Wichtige Debug-Punkte

1. **Variable nicht gefunden**: Prüfe `getVariablesByName()` Rückgabe
2. **Falsche GUI-Komponente**: Prüfe `variable.gui.component_id`
3. **Override-Probleme**: Prüfe `getVariableState()` Ergebnis
4. **Hierarchie-Probleme**: Prüfe Tree-Struktur in `data.tree`

## Erweiterungen

### Neue Input-Komponente hinzufügen

1. Komponente in `InputRenderer.tsx` registrieren
2. GUI-Konfiguration in Datenbank definieren
3. TypeScript-Typen erweitern falls nötig

### Neue Override-Ebene hinzufügen

1. Neue Tabelle in Datenbank erstellen
2. RPC-Funktion `get_variable_tree_with_context` erweitern
3. `useVariableTree` Hook anpassen
4. UI-Komponenten erweitern

## Beispiel-Szenarien

### Szenario 1: Template-Variable mit Dataset-Override

**Template-Ebene:**
```json
{
  "name": "DISABLE_PHASE",
  "data": false,
  "gui": {"component_id": "Checkbox", "label": "Disable Phase"},
  "source_level": "Template"
}
```

**Dataset-Ebene (Override):**
```json
{
  "name": "DISABLE_PHASE",
  "data": true,
  "source_level": "Dataset"
}
```

**Ergebnis:** Dataset-Wert (true) wird verwendet, GUI-Config vom Template übernommen.

### Szenario 2: Dataset-spezifische Variable

**Nur Dataset-Ebene:**
```json
{
  "name": "GNSSES",
  "data": ["G", "E", "R"],
  "gui": {
    "component_id": "CheckboxGroup",
    "items": [
      {"label": "GPS", "value": "G"},
      {"label": "Galileo", "value": "E"}
    ]
  },
  "source_level": "Dataset"
}
```

**Ergebnis:** Variable nur für dieses Dataset verfügbar.

### Szenario 3: Kategorie-Vererbung

**Root-Kategorie:**
```json
{"name": "ENVIRONMENT", "data": "outdoor"}
```

**Sub-Kategorie:** (erbt automatisch)
- Alle Datasets in Sub-Kategorie haben `ENVIRONMENT = "outdoor"`
- Kann durch Dataset-Variable überschrieben werden

## Performance-Überlegungen

### Caching
- `useVariableTree` Hook cached API-Responses
- Automatisches Refetch alle 30 Sekunden (konfigurierbar)
- Manuelle Refetch-Funktion verfügbar

### Optimierungen
- RPC-Funktion berechnet Override-Status in Datenbank
- Frontend normalisiert nur Datenformat
- Tree-Building erfolgt einmalig beim Laden

## Fehlerbehandlung

### Häufige Probleme

1. **Variable ohne GUI-Config wird nicht als Input angezeigt**
   - Lösung: `gui.component_id` in Datenbank setzen

2. **Dataset-Variable wird nicht gefunden**
   - Lösung: Prüfe `getVariablesByName()` Normalisierung

3. **Override funktioniert nicht**
   - Lösung: Prüfe Hierarchie-Pfad in RPC-Funktion

4. **Input-Komponente rendert nicht**
   - Lösung: Prüfe `InputRenderer` Komponenten-Mapping

## Migration und Updates

### Datenbank-Änderungen
- Neue GUI-Komponenten: Erweitere `gui` JSONB-Feld
- Neue Override-Ebenen: Neue Tabelle + RPC-Update
- Schema-Änderungen: Migration-Scripts verwenden

### Code-Änderungen
- TypeScript-Interfaces in `route.ts` aktualisieren
- Hook-Funktionen erweitern
- UI-Komponenten anpassen

## Testing

### Unit Tests
- `useVariableTree` Hook-Funktionen
- Variable-Override-Logik
- GUI-Komponenten-Rendering

### Integration Tests
- API-Endpunkt mit verschiedenen Template-IDs
- Vollständiger Datenfluss: DB → API → UI
- Override-Szenarien testen
